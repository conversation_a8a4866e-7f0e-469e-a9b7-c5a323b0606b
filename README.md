# SonarQube Community Edition Ansible Setup

Ansible project for automated SonarQube Community Edition installation with <PERSON><PERSON>.

## Prerequisites

- Ansible 2.9+
- SSH access to target servers
- Sudo privileges on target hosts
- Docker installed on target servers

## Structure

```
├── ansible.cfg              # Ansible configuration
├── requirements.yml         # Ansible collection dependencies
├── inventories/
│   └── production.ini       # Production inventory
├── roles/
│   └── sonarqube/           # SonarQube installation role
│       ├── defaults/        # Default variables
│       ├── tasks/           # Installation tasks
│       ├── templates/       # Configuration templates
│       └── vars/            # Role variables
└── playbooks/
    ├── sonar-install.yml    # SonarQube installation
    └── cleanup-sonarqube.yml # Complete SonarQube removal
```

## Configuration

### Environment Variables
To avoid committing usernames to the repository, use environment variables:

```bash
# Set your remote username (add to ~/.zshrc for persistence)
export ANSIBLE_REMOTE_USER=your-username
```

### Inventory Setup
```ini
# inventories/production.ini
[sonar]
apu041sonarq01.dach041.dachser.com ansible_python_interpreter=/usr/bin/python3.12
```

**Note:** If you prefer, you can add `ansible_user=your-username` in the inventories:
```ini
[my-server]
my-server.domain.com ansible_user=your-username ansible_python_interpreter=/usr/bin/python3.12
```

## Deployment

### Install Required Collections
```bash
# Install Ansible collections first
ansible-galaxy install -r requirements.yml
```

### Test Connectivity
```bash
# Test SSH connection to hosts
ansible sonar -m ping --ask-pass
```

### SonarQube Installation
```bash
# Dry run (check what will be changed)
ansible-playbook playbooks/sonar-install.yml --check --ask-pass --ask-become-pass

# Install SonarQube (requires both SSH and sudo passwords)
ansible-playbook playbooks/sonar-install.yml --ask-pass --ask-become-pass

# Deploy to specific host
ansible-playbook playbooks/sonar-install.yml -l apu041sonarq01.dach041.dachser.com --ask-pass --ask-become-pass
```

### SonarQube Cleanup
```bash
# Completely remove SonarQube (DESTRUCTIVE - removes all data)
ansible-playbook playbooks/cleanup-sonarqube.yml --ask-pass --ask-become-pass
```

### Password Requirements
- `--ask-pass`: SSH connection password
- `--ask-become-pass`: sudo/root password (required for system-level tasks)

## SonarQube Access

After installation, SonarQube will be available at:

- **HTTPS**: `https://[server-hostname]` (recommended)
- **HTTP**: `http://[server-hostname]` (redirects to HTTPS)
- **Default credentials**: admin/admin
- **Change password**: Required on first login

### SSL Certificate

**Option 1: Use Company Certificates (Recommended)**

Configure your company certificates in the inventory or defaults:

```yaml
# In your inventory file or roles/sonarqube/defaults/main.yml
ssl_use_company_cert: true
ssl_company_cert_path: "/path/to/your/company.crt"
ssl_company_key_path: "/path/to/your/company.key"
```

Then run the playbook:
```bash
ansible-playbook playbooks/sonar-install.yml --ask-pass --ask-become-pass
```

**Option 2: Manual certificate replacement**

If you prefer to copy certificates manually:

```bash
# Copy certificates to the Ansible control machine first
# Then update the paths in your configuration:
ssl_company_cert_path: "files/ssl/company.crt"
ssl_company_key_path: "files/ssl/company.key"
```

**Option 3: Self-signed certificates (Development only)**

```yaml
ssl_use_company_cert: false
```

**Option 4: Disable HTTPS (not recommended)**
Set `nginx_enabled: false` in your inventory to use direct HTTP access on port 9000.

## Troubleshooting

### Database Connection Issues

**SQL Server SSL Certificate Errors**
If you see SSL certificate validation errors like:
```
PKIX path building failed: unable to find valid certification path to requested target
```

**Solution 1: Disable SSL encryption (recommended for internal networks)**
```yaml
# In roles/sonarqube/defaults/main.yml or your inventory
sonar_db_encrypt: "false"
sonar_db_trust_server_certificate: "true"
```

**Solution 2: Enable SSL with certificate bypass (for SSL-required environments)**
```yaml
# In roles/sonarqube/defaults/main.yml or your inventory
sonar_db_encrypt: "true"
sonar_db_trust_server_certificate: "true"
```

**Solution 3: Proper SSL configuration (production)**
```yaml
# Configure proper certificates in SQL Server and set:
sonar_db_encrypt: "true"
sonar_db_trust_server_certificate: "false"
```

### Connectivity
- Test connectivity: `ansible sonar -m ping --ask-pass`
- Check containers: `docker ps | grep -E "(sonarqube|nginx)"`
- View SonarQube logs: `docker logs sonarqube`
- View nginx logs: `docker logs nginx-sonarqube`

### SSL Certificate Issues
- **Browser warning**: Normal with self-signed certificates - click "Advanced" → "Continue"
- **Replace certificates**: Copy your company certificates to `/opt/sonarqube/nginx/certs/`
- **Disable HTTPS**: Set `nginx_enabled: false` in inventory

### Port Issues
- **HTTPS**: `netstat -tlnp | grep 443`
- **HTTP**: `netstat -tlnp | grep 80`
- **SonarQube direct** (if nginx disabled): `netstat -tlnp | grep 9000`
