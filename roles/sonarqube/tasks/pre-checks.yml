---
# Pre-installation checks
- name: Get user groups
  ansible.builtin.command: groups
  register: user_groups
  changed_when: false
  become: false

- name: Check if user is in docker group
  ansible.builtin.fail:
    msg: "User {{ ansible_user }} is not in docker group. Run Docker installation playbook first."
  when: "'docker' not in user_groups.stdout"
  become: false

- name: Verify Docker is accessible
  ansible.builtin.command: docker info
  register: docker_check
  failed_when: docker_check.rc != 0
  changed_when: false
  become: false

- name: Display Docker info
  ansible.builtin.debug:
    msg: "✅ Docker is accessible and working"
