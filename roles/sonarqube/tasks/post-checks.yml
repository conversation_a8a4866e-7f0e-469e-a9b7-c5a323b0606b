---
# Post-installation verification
- name: Verify SonarQube is running
  ansible.builtin.uri:
    url: "{{ 'https' if nginx_enabled else 'http' }}://{{ ansible_host }}:{{ nginx_ssl_port if nginx_enabled else sonar_port | default('9000') }}"
    method: GET
    status_code: 200
    validate_certs: false
  register: final_check
  retries: 5
  delay: 10
  become: false

- name: Installation complete
  ansible.builtin.debug:
    msg:
      - "🎉 SonarQube installation completed successfully!"
      - "🌐 Access: {{ 'https' if nginx_enabled else 'http' }}://{{ ansible_host }}:{{ nginx_ssl_port if nginx_enabled else sonar_port | default('9000') }}"
      - "{% if nginx_enabled %}🔒 Nginx reverse proxy with SSL enabled{% endif %}"
      - "🔑 Default credentials: admin/admin"
      - "📊 Change password on first login"
