services:
  sonarqube:
    image: "{{ sonar_image }}:{{ sonar_version }}"
    container_name: sonarqube
    restart: unless-stopped
{% if nginx_enabled %}
    expose:
      - "9000"
{% else %}
    ports:
      - "{{ sonar_port }}:9000"
{% endif %}
    volumes:
      - "{{ sonar_data_dir }}/data:/opt/sonarqube/data"
      - "{{ sonar_data_dir }}/logs:/opt/sonarqube/logs"
      - "{{ sonar_data_dir }}/extensions:/opt/sonarqube/extensions"
    environment:
      - SONAR_ES_BOOTSTRAP_CHECKS_DISABLE=true
{% if sonar_db_type is defined %}
      - SONAR_JDBC_URL=jdbc:{{ sonar_db_type }}://{{ sonar_db_host }};databaseName={{ sonar_db_name }}{% if sonar_db_instance is defined and sonar_db_instance %};instanceName={{ sonar_db_instance }}{% endif %};encrypt={{ sonar_db_encrypt | default('false') }};trustServerCertificate={{ sonar_db_trust_server_certificate | default('true') }}

      - SONAR_JDBC_USERNAME={{ sonar_db_user }}
      - SONAR_JDBC_PASSWORD={{ sonar_db_password }}
{% endif %}
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
{% if nginx_enabled %}
    networks:
      - sonarqube

  nginx:
    image: "{{ nginx_image }}:{{ nginx_version }}"
    container_name: nginx-sonarqube
    restart: unless-stopped
    ports:
      - "{{ nginx_port }}:80"
      - "{{ nginx_ssl_port }}:443"
    volumes:
      - "{{ sonar_data_dir }}/nginx/nginx.conf:/etc/nginx/conf.d/default.conf:ro"
      - "{{ sonar_data_dir }}/nginx/certs:/etc/nginx/certs:ro"
    depends_on:
      - sonarqube
    networks:
      - sonarqube

networks:
  sonarqube:
    driver: bridge
{% endif %}
