---
- name: Complete SonarQube Cleanup - Remove All Data and Containers
  hosts: sonar
  gather_facts: false
  vars:
    sonar_data_dir: "/opt/sonarqube"
    
  tasks:
    - name: "🚨 WARNING: This will completely remove ALL SonarQube data!"
      ansible.builtin.pause:
        prompt: |
          
          ⚠️  DESTRUCTIVE OPERATION WARNING ⚠️
          
          This playbook will PERMANENTLY DELETE:
          • All SonarQube containers and images
          • All SonarQube data (projects, users, settings)
          • All Elasticsearch data and indexes
          • All configuration files
          • All Docker volumes and networks
          
          This action CANNOT BE UNDONE!
          
          Type 'YES' to proceed with complete cleanup
        echo: yes
      register: confirmation
      
    - name: Abort if not confirmed
      ansible.builtin.fail:
        msg: "❌ Cleanup cancelled. Only 'YES' confirmation accepts the destructive operation."
      when: confirmation.user_input != "YES"

    - name: "🧹 Step 1: Stop and remove all SonarQube containers"
      become: false
      block:
        - name: Stop all SonarQube containers
          ansible.builtin.shell: |
            docker stop $(docker ps -q --filter "name=sonarqube") 2>/dev/null || true
            docker stop $(docker ps -q --filter "ancestor=*sonarqube*") 2>/dev/null || true
          ignore_errors: true

        - name: Remove all SonarQube containers
          ansible.builtin.shell: |
            docker rm -f $(docker ps -aq --filter "name=sonarqube") 2>/dev/null || true  
            docker rm -f $(docker ps -aq --filter "ancestor=*sonarqube*") 2>/dev/null || true
          ignore_errors: true

        - name: Remove SonarQube Docker images
          ansible.builtin.shell: |
            docker rmi $(docker images "*sonarqube*" -q) 2>/dev/null || true
          ignore_errors: true

    - name: "🧹 Step 2: Clean up Docker Compose projects"
      become: false
      block:
        - name: Check if compose directory exists
          ansible.builtin.stat:
            path: "{{ sonar_data_dir }}/compose"
          register: compose_dir

        - name: Stop compose project if exists
          ansible.builtin.shell: docker compose down --remove-orphans --volumes
          args:
            chdir: "{{ sonar_data_dir }}/compose"
          when: compose_dir.stat.exists

        - name: Display compose cleanup status
          ansible.builtin.debug:
            msg: "{{ '✅ Stopped Docker Compose project' if compose_dir.stat.exists else '✅ No Docker Compose project found' }}"

        - name: Remove compose networks
          ansible.builtin.shell: |
            docker network rm $(docker network ls --filter "name=compose" -q) 2>/dev/null || true
            docker network rm $(docker network ls --filter "name=sonarqube" -q) 2>/dev/null || true
          ignore_errors: true

    - name: "🧹 Step 3: Kill any remaining processes"
      become: true
      block:
        - name: Check for Elasticsearch processes
          ansible.builtin.shell: pgrep -f elasticsearch
          register: es_processes
          failed_when: false
          changed_when: false

        - name: Kill Elasticsearch processes
          ansible.builtin.shell: pkill -9 -f elasticsearch
          when: es_processes.rc == 0
          failed_when: false

        - name: Display Elasticsearch cleanup status
          ansible.builtin.debug:
            msg: "{{ '✅ Killed ' + (es_processes.stdout_lines | length | string) + ' Elasticsearch processes' if es_processes.rc == 0 else '✅ No Elasticsearch processes found' }}"

        - name: Check for SonarQube processes
          ansible.builtin.shell: pgrep -f sonarqube
          register: sonar_processes
          failed_when: false
          changed_when: false

        - name: Kill SonarQube processes
          ansible.builtin.shell: pkill -9 -f sonarqube
          when: sonar_processes.rc == 0
          failed_when: false

        - name: Display SonarQube cleanup status
          ansible.builtin.debug:
            msg: "{{ '✅ Killed ' + (sonar_processes.stdout_lines | length | string) + ' SonarQube processes' if sonar_processes.rc == 0 else '✅ No SonarQube processes found' }}"

        - name: Check for processes using port 9000
          ansible.builtin.shell: fuser 9000/tcp
          register: port_processes
          failed_when: false
          changed_when: false

        - name: Kill processes using port 9000
          ansible.builtin.shell: fuser -k 9000/tcp
          when: port_processes.rc == 0
          failed_when: false

        - name: Display port cleanup status
          ansible.builtin.debug:
            msg: "{{ '✅ Killed processes using port 9000' if port_processes.rc == 0 else '✅ No processes using port 9000' }}"

    - name: "🧹 Step 4: Remove all SonarQube data directories"
      become: true
      block:
        - name: Remove complete SonarQube directory
          ansible.builtin.file:
            path: "{{ sonar_data_dir }}"
            state: absent

        - name: Remove any other SonarQube directories
          ansible.builtin.shell: |
            find /opt -name "*sonar*" -type d -exec rm -rf {} + 2>/dev/null || true
            find /var/lib -name "*sonar*" -type d -exec rm -rf {} + 2>/dev/null || true
            find /tmp -name "*sonar*" -type d -exec rm -rf {} + 2>/dev/null || true
          ignore_errors: true

    - name: "🧹 Step 5: Clean up Docker volumes"
      become: false
      block:
        - name: Remove all Docker volumes containing 'sonar'
          ansible.builtin.shell: |
            docker volume rm $(docker volume ls --filter "name=*sonar*" -q) 2>/dev/null || true
          ignore_errors: true

        - name: Prune unused Docker volumes
          ansible.builtin.shell: docker volume prune -f
          ignore_errors: true

    - name: "🧹 Step 6: Final system cleanup"
      become: true
      block:
        - name: Clear any remaining lock files system-wide
          ansible.builtin.shell: |
            find /tmp -name "*sonar*.lock" -delete 2>/dev/null || true
            find /var/tmp -name "*sonar*.lock" -delete 2>/dev/null || true
          ignore_errors: true

        - name: Clear Docker build cache
          ansible.builtin.shell: docker system prune -f
          become: false
          ignore_errors: true

    - name: "✅ Cleanup completed successfully!"
      ansible.builtin.debug:
        msg:
          - "🎉 SonarQube cleanup completed successfully!"
          - ""
          - "✅ All containers removed"
          - "✅ All data directories deleted"  
          - "✅ All processes terminated"
          - "✅ All Docker volumes cleaned"
          - "✅ All lock files removed"
          - ""
          - "🚀 System is now ready for fresh SonarQube installation"
          - ""
          - "Next step: Run 'ansible-playbook install-sonarqube.yml'"
